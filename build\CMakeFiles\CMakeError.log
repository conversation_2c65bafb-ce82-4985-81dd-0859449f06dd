Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: -Aa 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: -D__CLASSIC_C__ 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: -Aa 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: -D__CLASSIC_C__ 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: --c++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: --ec++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: --c++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: --ec++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
Checking whether the ASM compiler is GNU using "--version" did not match "(GNU assembler)|(GCC)|(Free Software Foundation)":
Checking whether the ASM compiler is Clang using "--version" did not match "(clang version)":
Checking whether the ASM compiler is AppleClang using "--version" did not match "(Apple LLVM version)":
Checking whether the ASM compiler is ARMClang using "--version" did not match "armclang":
Checking whether the ASM compiler is HP using "-V" did not match "HP C":
Checking whether the ASM compiler is Intel using "--version" did not match "(ICC)":
Checking whether the ASM compiler is IntelLLVM using "--version" did not match "(Intel[^
]+oneAPI)":
Checking whether the ASM compiler is SunPro using "-V" did not match "Sun C":
Checking whether the ASM compiler is XL using "-qversion" did not match "XL C":
Checking whether the ASM compiler is MSVC using "-?" did not match "Microsoft":
Checking whether the ASM compiler is TI using "-h" did not match "Texas Instruments":
Checking whether the ASM compiler is IAR using "" did not match "IAR Assembler":
Checking whether the ASM compiler is ARMCC using "" did not match "(ARM Compiler)|(ARM Assembler)":
Checking whether the ASM compiler is NASM using "-v" did not match "(NASM version)":
Checking whether the ASM compiler is YASM using "--version" did not match "(yasm)":
Checking whether the ASM compiler is ADSP using "-version" did not match "Analog Devices":
Checking whether the ASM compiler is QCC using "-V" did not match "gcc_nto":
Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: -Aa 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: -D__CLASSIC_C__ 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: -Aa 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: -D__CLASSIC_C__ 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: --c++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: --ec++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: --c++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: --ec++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
Checking whether the ASM compiler is GNU using "--version" did not match "(GNU assembler)|(GCC)|(Free Software Foundation)":
Checking whether the ASM compiler is Clang using "--version" did not match "(clang version)":
Checking whether the ASM compiler is AppleClang using "--version" did not match "(Apple LLVM version)":
Checking whether the ASM compiler is ARMClang using "--version" did not match "armclang":
Checking whether the ASM compiler is HP using "-V" did not match "HP C":
Checking whether the ASM compiler is Intel using "--version" did not match "(ICC)":
Checking whether the ASM compiler is IntelLLVM using "--version" did not match "(Intel[^
]+oneAPI)":
Checking whether the ASM compiler is SunPro using "-V" did not match "Sun C":
Checking whether the ASM compiler is XL using "-qversion" did not match "XL C":
Checking whether the ASM compiler is MSVC using "-?" did not match "Microsoft":
Checking whether the ASM compiler is TI using "-h" did not match "Texas Instruments":
Checking whether the ASM compiler is IAR using "" did not match "IAR Assembler":
Checking whether the ASM compiler is ARMCC using "" did not match "(ARM Compiler)|(ARM Assembler)":
Checking whether the ASM compiler is NASM using "-v" did not match "(NASM version)":
Checking whether the ASM compiler is YASM using "--version" did not match "(yasm)":
Checking whether the ASM compiler is ADSP using "-version" did not match "Analog Devices":
Checking whether the ASM compiler is QCC using "-V" did not match "gcc_nto":
Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: -Aa 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: -D__CLASSIC_C__ 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: -march=rv32imc
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: -Aa 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: -D__CLASSIC_C__ 

The output was:
系统找不到指定的文件。


Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
Compiler: riscv32-esp-elf-gcc 
Build flags: 
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
Checking whether the C compiler is IAR using "" did not match "IAR .+ Compiler":
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: --c++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: --ec++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: -march=rv32imc
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags:  

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: -c 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: --c++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: --ec++ 

The output was:
系统找不到指定的文件。


Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: riscv32-esp-elf-g++ 
Build flags: 
Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 

The output was:
系统找不到指定的文件。


Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
Checking whether the ASM compiler is GNU using "--version" did not match "(GNU assembler)|(GCC)|(Free Software Foundation)":
Checking whether the ASM compiler is Clang using "--version" did not match "(clang version)":
Checking whether the ASM compiler is AppleClang using "--version" did not match "(Apple LLVM version)":
Checking whether the ASM compiler is ARMClang using "--version" did not match "armclang":
Checking whether the ASM compiler is HP using "-V" did not match "HP C":
Checking whether the ASM compiler is Intel using "--version" did not match "(ICC)":
Checking whether the ASM compiler is IntelLLVM using "--version" did not match "(Intel[^
]+oneAPI)":
Checking whether the ASM compiler is SunPro using "-V" did not match "Sun C":
Checking whether the ASM compiler is XL using "-qversion" did not match "XL C":
Checking whether the ASM compiler is MSVC using "-?" did not match "Microsoft":
Checking whether the ASM compiler is TI using "-h" did not match "Texas Instruments":
Checking whether the ASM compiler is IAR using "" did not match "IAR Assembler":
Checking whether the ASM compiler is ARMCC using "" did not match "(ARM Compiler)|(ARM Assembler)":
Checking whether the ASM compiler is NASM using "-v" did not match "(NASM version)":
Checking whether the ASM compiler is YASM using "--version" did not match "(yasm)":
Checking whether the ASM compiler is ADSP using "-version" did not match "Analog Devices":
Checking whether the ASM compiler is QCC using "-V" did not match "gcc_nto":
