{"project_name": "bootloader", "project_path": "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject", "build_dir": "D:/Personal/Downloads/station-code-master/build/bootloader", "config_file": "D:/Personal/Downloads/station-code-master/sdkconfig", "config_defaults": "", "app_elf": "bootloader.elf", "app_bin": "bootloader.bin", "git_revision": "v4.4.1-dirty", "target": "esp32c3", "rev": "", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "riscv32-esp-elf-", "config_environment": {"COMPONENT_KCONFIGS": "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp32c3/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_system/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/freertos/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/Kconfig.projbuild;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/Kconfig.projbuild;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py/Kconfig.projbuild;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/partition_table/Kconfig.projbuild"}, "build_components": ["bootloader", "bootloader_support", "efuse", "esp32c3", "esp_common", "esp_hw_support", "esp_rom", "esp_system", "esptool_py", "freertos", "hal", "log", "main", "micro-ecc", "newlib", "partition_table", "riscv", "soc", "spi_flash", ""], "build_component_paths": ["F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp32c3", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_system", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/freertos", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/main", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/partition_table", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash", ""]}