{"project_name": "wifi_TcpClient", "project_path": "D:/Personal/Downloads/station-code-master", "build_dir": "D:/Personal/Downloads/station-code-master/build", "config_file": "D:/Personal/Downloads/station-code-master/sdkconfig", "config_defaults": "", "app_elf": "wifi_TcpClient.elf", "app_bin": "wifi_TcpClient.bin", "git_revision": "v4.4.1-dirty", "target": "esp32c3", "rev": "", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "riscv32-esp-elf-", "config_environment": {"COMPONENT_KCONFIGS": "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/app_trace/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/asio/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bt/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/coap/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/driver/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp-tls/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp32c3/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_adc_cal/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_eth/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_event/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_gdbstub/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_http_client/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_http_server/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_https_ota/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_https_server/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_ipc/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_lcd/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_netif/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_phy/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_pm/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_system/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_timer/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/espcoredump/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/fatfs/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/freemodbus/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/freertos/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/heap/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/ieee802154/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/jsmn/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/libsodium/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/lwip/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mdns/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mqtt/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/nvs_flash/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/openssl/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/openthread/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/pthread/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spiffs/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/tcp_transport/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/tinyusb/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/unity/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/usb/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/vfs/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/wear_levelling/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/wifi_provisioning/Kconfig;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/wpa_supplicant/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/app_update/Kconfig.projbuild;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/Kconfig.projbuild;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/Kconfig.projbuild;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py/Kconfig.projbuild;F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/partition_table/Kconfig.projbuild"}, "build_components": ["app_trace", "app_update", "asio", "bootloader", "bootloader_support", "bt", "cbor", "cmock", "coap", "console", "cxx", "driver", "efuse", "esp-tls", "esp32c3", "esp_adc_cal", "esp_common", "esp_eth", "esp_event", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_ipc", "esp_lcd", "esp_local_ctrl", "esp_netif", "esp_phy", "esp_pm", "esp_ringbuf", "esp_rom", "esp_serial_slave_link", "esp_system", "esp_timer", "esp_websocket_client", "esp_wifi", "espcoredump", "esptool_py", "expat", "fatfs", "freemodbus", "freertos", "hal", "heap", "idf_test", "ieee802154", "jsmn", "json", "lcd", "led", "libsodium", "log", "lwip", "main", "mbedtls", "mdns", "mqtt", "newlib", "nghttp", "nvs_flash", "openssl", "openthread", "partition_table", "protobuf-c", "protocomm", "pthread", "riscv", "sdmmc", "soc", "spi_flash", "spiffs", "tcp", "tcp_transport", "tcpip_adapter", "<PERSON><PERSON><PERSON>", "unity", "usb", "vfs", "wear_levelling", "wifi", "wifi_provisioning", "wpa_supplicant", ""], "build_component_paths": ["F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/app_trace", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/app_update", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/asio", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bt", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/cbor", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/cmock", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/coap", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/console", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/cxx", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/driver", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp-tls", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp32c3", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_adc_cal", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_eth", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_event", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_gdbstub", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hid", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_http_client", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_http_server", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_https_ota", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_https_server", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_ipc", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_lcd", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_local_ctrl", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_netif", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_phy", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_pm", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_ringbuf", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_serial_slave_link", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_system", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_timer", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_websocket_client", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_wifi", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/espcoredump", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esptool_py", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/expat", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/fatfs", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/freemodbus", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/freertos", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/heap", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/idf_test", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/ieee802154", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/jsmn", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/json", "D:/Personal/Downloads/station-code-master/components/lcd", "D:/Personal/Downloads/station-code-master/components/led", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/libsodium", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/lwip", "D:/Personal/Downloads/station-code-master/main", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mbedtls", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mdns", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/mqtt", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/nghttp", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/nvs_flash", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/openssl", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/openthread", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/partition_table", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/protobuf-c", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/protocomm", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/pthread", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/sdmmc", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spiffs", "D:/Personal/Downloads/station-code-master/components/tcp", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/tcp_transport", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/tcpip_adapter", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/tinyusb", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/unity", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/usb", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/vfs", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/wear_levelling", "D:/Personal/Downloads/station-code-master/components/wifi", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/wifi_provisioning", "F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/wpa_supplicant", ""]}