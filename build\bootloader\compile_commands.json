[{"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -o CMakeFiles\\bootloader.elf.dir\\project_elf_src_esp32c3.c.obj -c D:\\Personal\\Downloads\\station-code-master\\build\\bootloader\\project_elf_src_esp32c3.c", "file": "D:/Personal/Downloads/station-code-master/build/bootloader/project_elf_src_esp32c3.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\wdt_hal_iram.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\hal\\wdt_hal_iram.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\hal\\wdt_hal_iram.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mpu_hal.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\hal\\mpu_hal.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\hal\\mpu_hal.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\cpu_hal.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\hal\\cpu_hal.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\hal\\cpu_hal.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\lldesc.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\lldesc.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\lldesc.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\soc_include_legacy_warn.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\soc_include_legacy_warn.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\soc_include_legacy_warn.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\adc_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\adc_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\adc_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\dedic_gpio_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\dedic_gpio_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\dedic_gpio_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\gdma_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\gdma_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\gdma_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\gpio_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\gpio_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\gpio_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\interrupts.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\interrupts.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\interrupts.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\spi_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\spi_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\spi_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\ledc_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\ledc_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\ledc_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\rmt_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\rmt_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\rmt_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\sigmadelta_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\sigmadelta_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\sigmadelta_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\i2s_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\i2s_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\i2s_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\i2c_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\i2c_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\i2c_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\uart_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\uart_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\uart_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c3\\timer_periph.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\timer_periph.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\soc\\esp32c3\\timer_periph.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\micro-ecc\\CMakeFiles\\__idf_micro-ecc.dir\\uECC_verify_antifault.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include/spi_flash -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\spi_flash\\CMakeFiles\\__idf_spi_flash.dir\\esp32c3\\spi_flash_rom_patch.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\spi_flash\\esp32c3\\spi_flash_rom_patch.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\spi_flash\\esp32c3\\spi_flash_rom_patch.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_common.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_common.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common_loader.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_common_loader.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_common_loader.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_init.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_clock_init.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_clock_init.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_flash.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_flash.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_flash.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_mem.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_mem.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_mem.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_random.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_random.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random_esp32c3.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_random_esp32c3.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_random_esp32c3.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_utility.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_utility.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_utility.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp_image_format.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\esp_image_format.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\esp_image_format.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_encrypt.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\flash_encrypt.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\flash_encrypt.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\secure_boot.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\secure_boot.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\secure_boot.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_partitions.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\flash_partitions.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\flash_partitions.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_qio_mode.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\flash_qio_mode.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\flash_qio_mode.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_flash_config_esp32c3.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_flash_config_esp32c3.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_flash_config_esp32c3.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_efuse_esp32c3.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_efuse_esp32c3.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_efuse_esp32c3.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_init.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_init.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_init.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_loader.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_clock_loader.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_clock_loader.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_console.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_console.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console_loader.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_console_loader.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_console_loader.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_panic.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_panic.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\bootloader_panic.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c3\\bootloader_sha.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\esp32c3\\bootloader_sha.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\esp32c3\\bootloader_sha.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c3\\bootloader_soc.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\esp32c3\\bootloader_soc.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\esp32c3\\bootloader_soc.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject/components/micro-ecc/micro-ecc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c3\\bootloader_esp32c3.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\esp32c3\\bootloader_esp32c3.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader_support\\src\\esp32c3\\bootloader_esp32c3.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c3\\esp_efuse_table.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\esp32c3\\esp_efuse_table.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\esp32c3\\esp_efuse_table.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c3\\esp_efuse_fields.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\esp32c3\\esp_efuse_fields.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\esp32c3\\esp_efuse_fields.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c3\\esp_efuse_rtc_calib.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\esp32c3\\esp_efuse_rtc_calib.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\esp32c3\\esp_efuse_rtc_calib.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c3\\esp_efuse_utility.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\esp32c3\\esp_efuse_utility.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\esp32c3\\esp_efuse_utility.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_api.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\src\\esp_efuse_api.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\src\\esp_efuse_api.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_fields.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\src\\esp_efuse_fields.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\src\\esp_efuse_fields.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_utility.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\src\\esp_efuse_utility.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\src\\esp_efuse_utility.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_api_key_esp32xx.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\src\\esp_efuse_api_key_esp32xx.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\efuse\\src\\esp_efuse_api_key_esp32xx.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_system\\CMakeFiles\\__idf_esp_system.dir\\esp_err.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_system\\esp_err.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_system\\esp_err.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\compare_set.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\compare_set.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\compare_set.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\cpu_util.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\cpu_util.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\cpu_util.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c3\\cpu_util_esp32c3.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\cpu_util_esp32c3.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\cpu_util_esp32c3.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c3\\rtc_clk_init.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_clk_init.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_clk_init.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c3\\rtc_clk.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_clk.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_clk.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c3\\rtc_init.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_init.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_init.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c3\\rtc_pm.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_pm.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_pm.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c3\\rtc_sleep.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_sleep.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_sleep.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c3\\rtc_time.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_time.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\rtc_time.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/../hal -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c3\\chip_info.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\chip_info.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_hw_support\\port\\esp32c3\\chip_info.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/efuse/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/spi_flash/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_common\\CMakeFiles\\__idf_esp_common.dir\\src\\esp_err_to_name.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_common\\src\\esp_err_to_name.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_common\\src\\esp_err_to_name.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_crc.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_rom\\patches\\esp_rom_crc.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_rom\\patches\\esp_rom_crc.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_sys.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_rom\\patches\\esp_rom_sys.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_rom\\patches\\esp_rom_sys.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_uart.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_rom\\patches\\esp_rom_uart.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_rom\\patches\\esp_rom_uart.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_tjpgd.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_rom\\patches\\esp_rom_tjpgd.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\esp_rom\\patches\\esp_rom_tjpgd.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\log\\log.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\log\\log.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log_buffers.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\log\\log_buffers.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\log\\log_buffers.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\log_noos.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\log\\log_noos.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\log\\log_noos.c"}, {"directory": "D:/Personal/Downloads/station-code-master/build/bootloader", "command": "F:\\kfa\\Espressif\\tools\\riscv32-esp-elf\\esp-2021r2-patch3-8.4.0\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -Iconfig -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/log/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/include/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_rom/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_common/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/include/soc/esp32c3 -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/esp_hw_support/port/esp32c3/private_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/hal/platform_port/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/newlib/platform_include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/riscv/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/. -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/soc/esp32c3/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include -IF:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader_support/include_bootloader -march=rv32imc -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -ggdb -Wno-error=format= -nostartfiles -Wno-format -Os -freorder-blocks -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1/components/bootloader/subproject=. -fmacro-prefix-map=F:/kfa/Espressif/frameworks/esp-idf-v4.4.1=IDF -fstrict-volatile-bitfields -Wno-error=unused-but-set-variable -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu99 -Wno-old-style-declaration -D_GNU_SOURCE -DIDF_VER=\\\"v4.4.1-dirty\\\" -DESP_PLATFORM -DBOOTLOADER_BUILD=1 -o esp-idf\\main\\CMakeFiles\\__idf_main.dir\\bootloader_start.c.obj -c F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader\\subproject\\main\\bootloader_start.c", "file": "F:\\kfa\\Espressif\\frameworks\\esp-idf-v4.4.1\\components\\bootloader\\subproject\\main\\bootloader_start.c"}]