set(CMAKE_ASM_COMPILER "F:/kfa/Espressif/tools/riscv32-esp-elf/esp-2021r2-patch3-8.4.0/riscv32-esp-elf/bin/riscv32-esp-elf-gcc.exe")
set(CMAKE_ASM_COMPILER_ARG1 "")
set(CMAKE_AR "F:/kfa/mingw64/bin/ar.exe")
set(CMAKE_ASM_COMPILER_AR "F:/kfa/Espressif/tools/riscv32-esp-elf/esp-2021r2-patch3-8.4.0/riscv32-esp-elf/bin/riscv32-esp-elf-gcc-ar.exe")
set(CMAKE_RANLIB "F:/kfa/mingw64/bin/ranlib.exe")
set(CMAKE_ASM_COMPILER_RANLIB "F:/kfa/Espressif/tools/riscv32-esp-elf/esp-2021r2-patch3-8.4.0/riscv32-esp-elf/bin/riscv32-esp-elf-gcc-ranlib.exe")
set(CMAKE_LINKER "F:/kfa/mingw64/bin/ld.exe")
set(CMAKE_MT "")
set(CMAKE_ASM_COMPILER_LOADED 1)
set(CMAKE_ASM_COMPILER_ID "GNU")
set(CMAKE_ASM_COMPILER_VERSION "")
set(CMAKE_ASM_COMPILER_ENV_VAR "ASM")




set(CMAKE_ASM_IGNORE_EXTENSIONS h;H;o;O;obj;OBJ;def;DEF;rc;RC)
set(CMAKE_ASM_LINKER_PREFERENCE 0)


